# Augment Reset Tool - PowerShell Version

This PowerShell script resets Augment extension data in VS Code and Cursor IDEs to enable multi-account usage. It's a PowerShell port of the original Python script by <PERSON><PERSON><PERSON><PERSON>.

## Features

- ✅ **Multi-IDE Support**: Works with both VS Code and Cursor
- ✅ **Safe Operations**: Creates backups before making changes
- ✅ **Process Management**: Automatically closes IDE processes when needed
- ✅ **Interactive & Command Line**: Supports both interactive menu and command-line usage
- ✅ **Multi-Account Support**: Resets trial periods and generates new telemetry IDs
- ✅ **Error Recovery**: Restores from backup if operations fail

## Requirements

- Windows PowerShell 5.1+ or PowerShell Core 6+
- VS Code or Cursor installed and run at least once
- SQLite support (one of the following):
  - `sqlite3.exe` (included in Windows 10+)
  - System.Data.SQLite assembly

## Quick Start

### Method 1: Double-click the batch file (Easiest)
1. Double-click `run_augment_reset.bat`
2. Follow the interactive menu

### Method 2: Run PowerShell script directly
```powershell
# Interactive mode
.\AugmentReset.ps1

# Command line mode - Reset VS Code completely
.\AugmentReset.ps1 -IDE vscode -Operation all

# Command line mode - Reset Cursor database only
.\AugmentReset.ps1 -IDE cursor -Operation database -Force
```

## Usage Options

### Interactive Mode
Run without parameters to use the interactive menu:
```powershell
.\AugmentReset.ps1
```

### Command Line Mode
```powershell
.\AugmentReset.ps1 [-IDE <vscode|cursor>] [-Operation <all|database|telemetry>] [-Force]
```

**Parameters:**
- `-IDE`: Target IDE (`vscode` or `cursor`)
- `-Operation`: 
  - `all` - Complete reset (recommended)
  - `database` - Clean database entries only
  - `telemetry` - Modify telemetry IDs only
- `-Force`: Skip confirmation prompts

## What the Script Does

### Database Cleaning
- Connects to the IDE's SQLite database (`state.vscdb`)
- Finds all entries containing 'augment'
- Removes these entries to reset extension state
- Creates backup before modification

### Telemetry ID Modification
- Modifies `storage.json` file
- Generates new `machineId` (64-character hex string)
- Generates new `devDeviceId` (UUID)
- Updates both root and telemetry sections

### Process Management
- Detects running IDE processes
- Gracefully closes processes with user confirmation
- Force-kills if graceful shutdown fails

## File Locations

The script automatically finds IDE files in standard locations:

**VS Code:**
- Database: `%APPDATA%\Code\User\globalStorage\state.vscdb`
- Storage: `%APPDATA%\Code\User\globalStorage\storage.json`

**Cursor:**
- Database: `%APPDATA%\Cursor\User\globalStorage\state.vscdb`
- Storage: `%APPDATA%\Cursor\User\globalStorage\storage.json`

## Safety Features

- **Automatic Backups**: Creates `.backup` files before modifications
- **Error Recovery**: Restores from backup if operations fail
- **Process Detection**: Warns if IDE is running and offers to close it
- **Validation**: Checks file existence before operations

## Troubleshooting

### SQLite Not Available
If you get "SQLite tools not available" error:

1. **Windows 10+**: SQLite should be included. Try running `sqlite3` in Command Prompt
2. **Older Windows**: Download sqlite3.exe from https://www.sqlite.org/download.html
3. **Alternative**: Install System.Data.SQLite NuGet package

### Permission Errors
If you get execution policy errors:
```powershell
# Run with bypass policy
powershell -ExecutionPolicy Bypass -File "AugmentReset.ps1"

# Or use the batch file which handles this automatically
run_augment_reset.bat
```

### IDE Not Detected
- Make sure VS Code or Cursor is installed
- Run the IDE at least once to create necessary files
- Check if files exist in the expected locations

## Examples

### Complete Reset for VS Code
```powershell
.\AugmentReset.ps1 -IDE vscode -Operation all
```

### Clean Database Only for Cursor
```powershell
.\AugmentReset.ps1 -IDE cursor -Operation database
```

### Silent Operation (No Prompts)
```powershell
.\AugmentReset.ps1 -IDE vscode -Operation all -Force
```

## Files Included

- `AugmentReset.ps1` - Main PowerShell script
- `run_augment_reset.bat` - Batch launcher (handles execution policy)
- `README.md` - This documentation
- `augment_reset.py` - Original Python version (reference)

## Credits

Based on the original Python script by **Nguyễn Thị Thu Mai**.
PowerShell port created for easier Windows integration and multi-account Augment usage.

## License

This tool is provided as-is for educational and legitimate multi-account usage purposes.
Use responsibly and in accordance with the terms of service of the respective IDEs and extensions.
