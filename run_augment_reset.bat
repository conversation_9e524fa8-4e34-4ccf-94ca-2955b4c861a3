@echo off
REM Augment Reset Tool Launcher
REM This batch file runs the PowerShell script with proper execution policy

echo Augment Reset Tool - PowerShell Launcher
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please install PowerShell or add it to your PATH
    pause
    exit /b 1
)

REM Check if the PowerShell script exists
if not exist "AugmentReset.ps1" (
    echo ERROR: AugmentReset.ps1 not found in current directory
    echo Please make sure the PowerShell script is in the same folder as this batch file
    pause
    exit /b 1
)

echo Running Augment Reset Tool...
echo.

REM Run the PowerShell script with bypass execution policy
powershell -ExecutionPolicy Bypass -File "AugmentReset.ps1" %*

echo.
echo Script execution completed.
pause
