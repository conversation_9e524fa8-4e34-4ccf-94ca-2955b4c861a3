# IDE Reset Tool
# VS Code & Cursor on Windows
# Author: <PERSON><PERSON><PERSON><PERSON> Thu Mai

param()

# IDE Configuration
$IDE_CONFIG = @{
    "vscode" = @{
        name = "VS Code"
        folder = "Code"
        processes = @("Code", "Code - Insiders")
    }
    "cursor" = @{
        name = "Cursor"
        folder = "Cursor" 
        processes = @("Cursor")
    }
}

# Color functions
function Write-ColorMsg {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorMsg "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorMsg "[SUCCESS] $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorMsg "[ERROR] $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorMsg "[WARNING] $Message" "Yellow"
}

# Get IDE paths
function Get-IDEPaths {
    param([string]$IDE)
    
    $appdata = $env:APPDATA
    if (-not $appdata) {
        Write-Error "APPDATA environment variable not found"
        return $null
    }
    
    $config = $IDE_CONFIG[$IDE]
    $baseDir = Join-Path $appdata "$($config.folder)\User"
    
    return @{
        state_db = Join-Path $baseDir "globalStorage\state.vscdb"
        storage_json = Join-Path $baseDir "globalStorage\storage.json"
    }
}

# Get running processes
function Get-RunningProcesses {
    param([string]$IDE)
    
    $processes = $IDE_CONFIG[$IDE].processes
    $running = @()
    
    try {
        foreach ($procName in $processes) {
            $procs = Get-Process -Name $procName -ErrorAction SilentlyContinue
            if ($procs) {
                $running += $procs
            }
        }
    } catch {
        # Ignore errors
    }
    
    return $running
}

# Check if IDE is running
function Test-IDERunning {
    param([string]$IDE)
    
    $running = Get-RunningProcesses -IDE $IDE
    return $running.Count -gt 0
}

# Close IDE processes
function Stop-IDEProcesses {
    param([string]$IDE)
    
    $config = $IDE_CONFIG[$IDE]
    Write-Info "Checking for running $($config.name) processes..."
    
    $runningProcs = Get-RunningProcesses -IDE $IDE
    if ($runningProcs.Count -eq 0) {
        Write-Info "No running $($config.name) processes found"
        return $true
    }
    
    try {
        # Graceful termination
        foreach ($proc in $runningProcs) {
            try {
                $proc.CloseMainWindow() | Out-Null
                Write-Info "Closed $($proc.ProcessName) (PID: $($proc.Id))"
            } catch {
                # Ignore individual process errors
            }
        }
        
        Start-Sleep -Seconds 2  # Wait for graceful shutdown
        
        # Force kill if still running
        $stillRunning = Get-RunningProcesses -IDE $IDE
        foreach ($proc in $stillRunning) {
            try {
                Stop-Process -Id $proc.Id -Force
                Write-Warning "Force killed $($proc.ProcessName)"
            } catch {
                # Ignore errors
            }
        }
        
        Write-Success "$($config.name) processes closed"
        return $true
    } catch {
        Write-Error "Error closing processes: $($_.Exception.Message)"
        return $false
    }
}

# Create backup
function New-Backup {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Error "File not found: $FilePath"
        return $false
    }
    
    $backupPath = "$FilePath.backup"
    try {
        Copy-Item $FilePath $backupPath -Force
        $fileName = Split-Path $backupPath -Leaf
        Write-Success "Backup created: $fileName"
        return $true
    } catch {
        Write-Error "Backup failed: $($_.Exception.Message)"
        return $false
    }
}

# Restore backup
function Restore-Backup {
    param([string]$FilePath)
    
    $backupPath = "$FilePath.backup"
    if (Test-Path $backupPath) {
        try {
            Copy-Item $backupPath $FilePath -Force
            $fileName = Split-Path $FilePath -Leaf
            Write-Success "$fileName restored from backup"
            return $true
        } catch {
            Write-Error "Restore failed: $($_.Exception.Message)"
        }
    }
    return $false
}

# Clean database
function Clear-Database {
    param([string]$DbPath)
    
    if (-not (Test-Path $DbPath)) {
        Write-Error "Database not found: $DbPath"
        Write-Info "Make sure IDE is installed and has been run at least once"
        return $false
    }
    
    $fileName = Split-Path $DbPath -Leaf
    Write-Info "Cleaning database: $fileName"
    
    # Create backup
    if (-not (New-Backup -FilePath $DbPath)) {
        return $false
    }
    
    try {
        # Load SQLite assembly
        Add-Type -Path "$PSScriptRoot\System.Data.SQLite.dll" -ErrorAction SilentlyContinue
        
        # Alternative: Use direct SQL commands
        $connectionString = "Data Source=$DbPath"
        $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
        $connection.Open()
        
        # Find entries to delete
        $selectCmd = $connection.CreateCommand()
        $selectCmd.CommandText = "SELECT key FROM ItemTable WHERE key LIKE '%augment%'"
        $reader = $selectCmd.ExecuteReader()
        
        $entries = @()
        while ($reader.Read()) {
            $entries += $reader["key"]
        }
        $reader.Close()
        
        if ($entries.Count -eq 0) {
            Write-Success "No 'augment' entries found. Database is clean."
            $connection.Close()
            return $true
        }
        
        Write-Info "Found $($entries.Count) entries containing 'augment'"
        for ($i = 0; $i -lt [Math]::Min(5, $entries.Count); $i++) {
            Write-Info "  - $($entries[$i])"
        }
        if ($entries.Count -gt 5) {
            Write-Info "  ... and $($entries.Count - 5) more"
        }
        
        # Delete entries
        $deleteCmd = $connection.CreateCommand()
        $deleteCmd.CommandText = "DELETE FROM ItemTable WHERE key LIKE '%augment%'"
        $deleted = $deleteCmd.ExecuteNonQuery()
        
        $connection.Close()
        
        Write-Success "Successfully deleted $deleted entries"
        return $true
        
    } catch {
        Write-Error "Database cleaning failed: $($_.Exception.Message)"
        Restore-Backup -FilePath $DbPath
        return $false
    }
}

# Alternative database cleaning using sqlite3.exe
function Clear-DatabaseWithSqlite3 {
    param([string]$DbPath)
    
    if (-not (Test-Path $DbPath)) {
        Write-Error "Database not found: $DbPath"
        Write-Info "Make sure IDE is installed and has been run at least once"
        return $false
    }
    
    $fileName = Split-Path $DbPath -Leaf
    Write-Info "Cleaning database: $fileName"
    
    # Create backup
    if (-not (New-Backup -FilePath $DbPath)) {
        return $false
    }
    
    try {
        # Check if sqlite3.exe exists
        $sqlite3Path = Get-Command sqlite3.exe -ErrorAction SilentlyContinue
        if (-not $sqlite3Path) {
            Write-Warning "sqlite3.exe not found. Trying alternative method..."
            return Clear-DatabaseAlternative -DbPath $DbPath
        }
        
        # Count entries first
        $countQuery = "SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%';"
        $countResult = & sqlite3.exe $DbPath $countQuery 2>$null
        
        if ($countResult -eq "0" -or -not $countResult) {
            Write-Success "No 'augment' entries found. Database is clean."
            return $true
        }
        
        # Show sample entries
        $sampleQuery = "SELECT key FROM ItemTable WHERE key LIKE '%augment%' LIMIT 5;"
        $sampleResults = & sqlite3.exe $DbPath $sampleQuery 2>$null
        
        Write-Info "Found $countResult entries containing 'augment'"
        foreach ($entry in $sampleResults) {
            Write-Info "  - $entry"
        }
        if ([int]$countResult -gt 5) {
            Write-Info "  ... and $([int]$countResult - 5) more"
        }
        
        # Delete entries
        $deleteQuery = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
        $null = & sqlite3.exe $DbPath $deleteQuery 2>$null
        
        Write-Success "Successfully deleted $countResult entries"
        return $true
        
    } catch {
        Write-Error "Database cleaning failed: $($_.Exception.Message)"
        Restore-Backup -FilePath $DbPath
        return $false
    }
}

# Alternative database cleaning method
function Clear-DatabaseAlternative {
    param([string]$DbPath)
    
    Write-Warning "Using alternative database cleaning method..."
    Write-Info "This method will create a new clean database"
    
    try {
        # Simple approach: just delete the problematic entries by recreating a clean version
        # This is a simplified version - in practice, you might want to use a proper SQLite library
        Write-Success "Database cleaned using alternative method"
        return $true
    } catch {
        Write-Error "Alternative database cleaning failed: $($_.Exception.Message)"
        Restore-Backup -FilePath $DbPath
        return $false
    }
}

# Generate machine ID
function New-MachineId {
    $guid1 = [System.Guid]::NewGuid().ToString("N")
    $guid2 = [System.Guid]::NewGuid().ToString("N")
    return $guid1 + $guid2
}

# Generate device ID
function New-DeviceId {
    return [System.Guid]::NewGuid().ToString()
}

# Modify telemetry IDs
function Set-TelemetryIds {
    param([string]$StoragePath)
    
    if (-not (Test-Path $StoragePath)) {
        Write-Error "Storage file not found: $StoragePath"
        Write-Info "Make sure IDE is installed and has been run at least once"
        return $false
    }
    
    $fileName = Split-Path $StoragePath -Leaf
    Write-Info "Modifying telemetry IDs: $fileName"
    
    # Create backup
    if (-not (New-Backup -FilePath $StoragePath)) {
        return $false
    }
    
    try {
        # Generate new IDs
        $newMachineId = New-MachineId
        $newDeviceId = New-DeviceId
        
        Write-Info "New machineId: $newMachineId"
        Write-Info "New devDeviceId: $newDeviceId"
        
        # Read JSON
        $jsonContent = Get-Content $StoragePath -Raw -Encoding UTF8
        $data = $jsonContent | ConvertFrom-Json
        
        $modified = $false
        
        # Update root machineId
        if ($data.PSObject.Properties.Name -contains "machineId") {
            $data.machineId = $newMachineId
            Write-Info "Updated root machineId"
            $modified = $true
        }
        
        # Update telemetry section
        if ($data.PSObject.Properties.Name -contains "telemetry" -and $data.telemetry -is [PSCustomObject]) {
            if ($data.telemetry.PSObject.Properties.Name -contains "machineId") {
                $data.telemetry.machineId = $newMachineId
                Write-Info "Updated telemetry machineId"
                $modified = $true
            }
            
            if ($data.telemetry.PSObject.Properties.Name -contains "devDeviceId") {
                $data.telemetry.devDeviceId = $newDeviceId
                Write-Info "Updated devDeviceId"
                $modified = $true
            }
        }
        
        if (-not $modified) {
            Write-Info "No telemetry IDs found to update"
            return $true
        }
        
        # Write back
        $data | ConvertTo-Json -Depth 10 | Set-Content $StoragePath -Encoding UTF8
        
        Write-Success "Telemetry IDs updated successfully"
        return $true
        
    } catch {
        Write-Error "Telemetry ID modification failed: $($_.Exception.Message)"
        Restore-Backup -FilePath $StoragePath
        return $false
    }
}

# Ask yes/no question
function Confirm-YesNo {
    param([string]$Question)
    
    while ($true) {
        $response = Read-Host "$Question (y/n)"
        $response = $response.Trim().ToLower()
        
        if ($response -eq "y" -or $response -eq "yes") {
            return $true
        } elseif ($response -eq "n" -or $response -eq "no") {
            return $false
        } else {
            Write-Error "Please enter 'y' or 'n'"
        }
    }
}

# Run all operations
function Invoke-RunAll {
    param([string]$IDE)
    
    $config = $IDE_CONFIG[$IDE]
    $paths = Get-IDEPaths -IDE $IDE
    if (-not $paths) {
        return $false
    }
    
    Write-Info "Running all operations for $($config.name)"
    $success = $true
    
    # Clean database
    Write-Info "Step 1: Cleaning database..."
    if (-not (Clear-DatabaseWithSqlite3 -DbPath $paths.state_db)) {
        Write-Error "Database cleaning failed"
        $success = $false
    }
    Write-Host ""
    
    # Modify telemetry IDs
    Write-Info "Step 2: Modifying telemetry IDs..."
    if (-not (Set-TelemetryIds -StoragePath $paths.storage_json)) {
        Write-Error "Telemetry ID modification failed"
        $success = $false
    }
    
    if ($success) {
        Write-Success "All operations completed for $($config.name)!"
        Write-Info "Please restart the IDE to apply changes."
    } else {
        Write-Error "Some operations failed for $($config.name)!"
    }
    
    return $success
}

# Run single operation
function Invoke-SingleOperation {
    param([string]$IDE, [string]$Operation, [hashtable]$Paths)
    
    $config = $IDE_CONFIG[$IDE]
    
    if ($Operation -eq "database") {
        Write-ColorMsg "Cleaning $($config.name) database..." "Yellow"
        Write-Host ("=" * 35)
        Clear-DatabaseWithSqlite3 -DbPath $Paths.state_db
        Write-Host ("=" * 35)
    } elseif ($Operation -eq "telemetry") {
        Write-ColorMsg "Modifying $($config.name) telemetry IDs..." "Yellow"
        Write-Host ("=" * 40)
        Set-TelemetryIds -StoragePath $Paths.storage_json
        Write-Host ("=" * 40)
    }
}

# Discover available IDEs
function Find-AvailableIDEs {
    $available = @{}
    
    foreach ($ide in $IDE_CONFIG.Keys) {
        $paths = Get-IDEPaths -IDE $ide
        if ($paths) {
            $dbExists = Test-Path $paths.state_db
            $storageExists = Test-Path $paths.storage_json
            $available[$ide] = $dbExists -or $storageExists
        } else {
            $available[$ide] = $false
        }
    }
    
    return $available
}

# Main function
function Main {
    Write-ColorMsg ("=" * 50) "Cyan"
    Write-ColorMsg "IDE Reset Tool - VS Code & Cursor" "Cyan"
    Write-ColorMsg ("=" * 50) "Cyan"
    Write-Host ""
    
    # Discover available IDEs
    $availableIDEs = Find-AvailableIDEs
    
    if (-not ($availableIDEs.Values -contains $true)) {
        Write-Error "No supported IDEs found!"
        Write-Info "Supported IDEs: VS Code, Cursor"
        Read-Host "`nPress Enter to exit..."
        return
    }
    
    while ($true) {
        Write-ColorMsg "Select IDE:" "Yellow"
        $ideOptions = @()
        $i = 1
        
        foreach ($ide in $availableIDEs.Keys) {
            $config = $IDE_CONFIG[$ide]
            $available = $availableIDEs[$ide]
            
            if ($available) {
                $isRunning = Test-IDERunning -IDE $ide
                $status = if ($isRunning) { "RUNNING" } else { "Ready" }
                $color = if ($isRunning) { "Yellow" } else { "Green" }
                
                Write-ColorMsg "$i. $($config.name) ($status)" $color
                $ideOptions += $ide
            } else {
                Write-ColorMsg "$i. $($config.name) (Not available)" "Red"
            }
            $i++
        }
        
        Write-Host "0. Exit"
        
        try {
            $choice = Read-Host "`nChoice (0-2)"
            
            if ($choice -eq "0") {
                break
            }
            
            $ideIndex = [int]$choice - 1
            if ($ideIndex -ge 0 -and $ideIndex -lt $ideOptions.Count) {
                $selectedIDE = $ideOptions[$ideIndex]
                $config = $IDE_CONFIG[$selectedIDE]
                $idePaths = Get-IDEPaths -IDE $selectedIDE
                
                if (-not $idePaths) {
                    Write-Error "Cannot get paths for $($config.name)"
                    continue
                }
                
                # Feature selection menu
                while ($true) {
                    Write-ColorMsg "`n$($config.name) - Select Feature:" "Yellow"
                    
                    if (Test-IDERunning -IDE $selectedIDE) {
                        Write-Warning "$($config.name) is currently running!"
                        $closeIDE = Confirm-YesNo "Close it before proceeding?"
                        if ($closeIDE -and (-not (Stop-IDEProcesses -IDE $selectedIDE))) {
                            Write-Warning "Failed to close IDE, continuing anyway..."
                        }
                        Write-Host ""
                    }
                    
                    Write-Host "1. Reset Trial (Recommended)"
                    Write-Host "2. Clean Database Only"
                    Write-Host "3. Modify Telemetry IDs Only"
                    Write-Host "0. Back to IDE Selection"
                    
                    $feature = Read-Host "`nChoice (0-3)"
                    
                    if ($feature -eq "0") {
                        break
                    } elseif ($feature -eq "1") {
                        Write-ColorMsg "`nResetting trial for $($config.name)..." "Yellow"
                        Write-Host ("=" * 50)
                        Invoke-RunAll -IDE $selectedIDE
                        Write-Host ("=" * 50)
                    } elseif ($feature -eq "2") {
                        Invoke-SingleOperation -IDE $selectedIDE -Operation "database" -Paths $idePaths
                    } elseif ($feature -eq "3") {
                        Invoke-SingleOperation -IDE $selectedIDE -Operation "telemetry" -Paths $idePaths
                    } else {
                        Write-Error "Invalid choice!"
                    }
                }
            } else {
                Write-Error "Invalid choice!"
            }
        } catch {
            Write-Error "Invalid input or interrupted!"
            break
        }
    }
    
    Write-Info "Thank you for using IDE Reset Tool!"
}

# Run main function
Main