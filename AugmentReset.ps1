<#
.SYNOPSIS
    Augment Reset Tool for Multi-Account Usage - PowerShell Version

.DESCRIPTION
    This script resets Augment extension data in VS Code and Cursor IDEs to allow multi-account usage.
    It cleans the database entries and modifies telemetry IDs to reset trial periods.

    Based on the Python version by <PERSON><PERSON><PERSON><PERSON> <PERSON>hu Mai.

.PARAMETER IDE
    Specify which IDE to reset: 'vscode' or 'cursor'

.PARAMETER Operation
    Specify operation: 'all' (recommended), 'database', or 'telemetry'

.PARAMETER Force
    Skip confirmation prompts

.EXAMPLE
    .\AugmentReset.ps1
    Run in interactive mode

.EXAMPLE
    .\AugmentReset.ps1 -IDE vscode -Operation all
    Reset VS Code with all operations

.EXAMPLE
    .\AugmentReset.ps1 -IDE cursor -Operation database -Force
    Clean Cursor database without prompts

.NOTES
    Requirements:
    - Windows PowerShell 5.1+ or PowerShell Core 6+
    - sqlite3.exe (included in Windows 10+) or System.Data.SQLite assembly
    - VS Code or Cursor installed and run at least once

    The script will:
    1. Close running IDE processes (with confirmation)
    2. Create backups of modified files
    3. Clean database entries containing 'augment'
    4. Generate new telemetry IDs for multi-account support
    5. <PERSON>ore from backup if operations fail
#>

# Augment Reset Tool for Multi-Account Usage
# PowerShell version of augment_reset.py
# Author: Based on Nguyễn Thị Thu Mai's Python version

param(
    [Parameter(HelpMessage="IDE to reset: 'vscode' or 'cursor'")]
    [ValidateSet("vscode", "cursor", "")]
    [string]$IDE = "",
    
    [Parameter(HelpMessage="Operation: 'all', 'database', 'telemetry'")]
    [ValidateSet("all", "database", "telemetry", "")]
    [string]$Operation = "",
    
    [Parameter(HelpMessage="Skip confirmation prompts")]
    [switch]$Force
)

# Color support for PowerShell
$Colors = @{
    Red = "Red"
    Green = "Green" 
    Yellow = "Yellow"
    Blue = "Cyan"
    Cyan = "Cyan"
}

# IDE Configuration
$IDEConfig = @{
    vscode = @{
        Name = "VS Code"
        Folder = "Code"
        Processes = @("Code", "Code - Insiders")
    }
    cursor = @{
        Name = "Cursor"
        Folder = "Cursor"
        Processes = @("Cursor")
    }
}

function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Prefix = ""
    )
    
    $fullMessage = if ($Prefix) { "[$Prefix] $Message" } else { $Message }
    
    if ($Colors.ContainsKey($Color)) {
        Write-Host $fullMessage -ForegroundColor $Colors[$Color]
    } else {
        Write-Host $fullMessage
    }
}

function Write-Info { param([string]$Message) Write-ColorMessage $Message "Blue" "INFO" }
function Write-Success { param([string]$Message) Write-ColorMessage $Message "Green" "SUCCESS" }
function Write-Error { param([string]$Message) Write-ColorMessage $Message "Red" "ERROR" }
function Write-Warning { param([string]$Message) Write-ColorMessage $Message "Yellow" "WARNING" }

function Get-IDEPaths {
    param([string]$IDEType)
    
    $appData = $env:APPDATA
    if (-not $appData) {
        Write-Error "APPDATA environment variable not found"
        return $null
    }
    
    $config = $IDEConfig[$IDEType]
    $baseDir = Join-Path $appData "$($config.Folder)\User"
    
    return @{
        StateDB = Join-Path $baseDir "globalStorage\state.vscdb"
        StorageJSON = Join-Path $baseDir "globalStorage\storage.json"
    }
}

function Get-RunningProcesses {
    param([string]$IDEType)
    
    $config = $IDEConfig[$IDEType]
    $running = @()
    
    foreach ($processName in $config.Processes) {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($processes) {
            $running += $processes
        }
    }
    
    return $running
}

function Test-IDERunning {
    param([string]$IDEType)
    
    $running = Get-RunningProcesses $IDEType
    return $running.Count -gt 0
}

function Stop-IDEProcesses {
    param([string]$IDEType)
    
    $config = $IDEConfig[$IDEType]
    Write-Info "Checking for running $($config.Name) processes..."
    
    $runningProcs = Get-RunningProcesses $IDEType
    if ($runningProcs.Count -eq 0) {
        Write-Info "No running $($config.Name) processes found"
        return $true
    }
    
    try {
        # Graceful termination
        foreach ($proc in $runningProcs) {
            try {
                $proc.CloseMainWindow() | Out-Null
                Write-Info "Closed $($proc.ProcessName) (PID: $($proc.Id))"
            }
            catch {
                # Ignore errors for individual processes
            }
        }
        
        Start-Sleep -Seconds 2
        
        # Force kill if still running
        $stillRunning = Get-RunningProcesses $IDEType
        foreach ($proc in $stillRunning) {
            try {
                Stop-Process -Id $proc.Id -Force
                Write-Warning "Force killed $($proc.ProcessName)"
            }
            catch {
                # Ignore errors
            }
        }
        
        Write-Success "$($config.Name) processes closed"
        return $true
    }
    catch {
        Write-Error "Error closing processes: $($_.Exception.Message)"
        return $false
    }
}

function New-Backup {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Error "File not found: $FilePath"
        return $false
    }
    
    $backupPath = "$FilePath.backup"
    try {
        Copy-Item $FilePath $backupPath -Force
        Write-Success "Backup created: $(Split-Path $backupPath -Leaf)"
        return $true
    }
    catch {
        Write-Error "Backup failed: $($_.Exception.Message)"
        return $false
    }
}

function Restore-Backup {
    param([string]$FilePath)
    
    $backupPath = "$FilePath.backup"
    if (Test-Path $backupPath) {
        try {
            Copy-Item $backupPath $FilePath -Force
            Write-Success "$(Split-Path $FilePath -Leaf) restored from backup"
            return $true
        }
        catch {
            Write-Error "Restore failed: $($_.Exception.Message)"
        }
    }
    return $false
}

function Clear-Database {
    param([string]$DBPath)
    
    if (-not (Test-Path $DBPath)) {
        Write-Error "Database not found: $DBPath"
        Write-Info "Make sure IDE is installed and has been run at least once"
        return $false
    }
    
    Write-Info "Cleaning database: $(Split-Path $DBPath -Leaf)"
    
    # Create backup
    if (-not (New-Backup $DBPath)) {
        return $false
    }
    
    try {
        # Try to use sqlite3.exe if available (comes with Windows 10+)
        $sqliteExe = Get-Command sqlite3.exe -ErrorAction SilentlyContinue

        if ($sqliteExe) {
            # Use sqlite3.exe command line tool
            $selectQuery = "SELECT key FROM ItemTable WHERE key LIKE '%augment%';"
            $entries = & sqlite3.exe $DBPath $selectQuery 2>$null

            if (-not $entries -or $entries.Count -eq 0) {
                Write-Success "No 'augment' entries found. Database is clean."
                return $true
            }

            Write-Info "Found $($entries.Count) entries containing 'augment'"
            for ($i = 0; $i -lt [Math]::Min(5, $entries.Count); $i++) {
                Write-Info "  - $($entries[$i])"
            }
            if ($entries.Count -gt 5) {
                Write-Info "  ... and $($entries.Count - 5) more"
            }

            # Delete entries
            $deleteQuery = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
            & sqlite3.exe $DBPath $deleteQuery 2>$null

            Write-Success "Successfully deleted entries containing 'augment'"
            return $true
        }
        else {
            # Fallback: Try to load System.Data.SQLite if available
            try {
                Add-Type -AssemblyName "System.Data.SQLite" -ErrorAction Stop

                $connectionString = "Data Source=$DBPath;Version=3;"
                $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
                $connection.Open()

                # Find entries to delete
                $selectCommand = $connection.CreateCommand()
                $selectCommand.CommandText = "SELECT key FROM ItemTable WHERE key LIKE '%augment%'"
                $reader = $selectCommand.ExecuteReader()

                $entries = @()
                while ($reader.Read()) {
                    $entries += $reader["key"]
                }
                $reader.Close()

                if ($entries.Count -eq 0) {
                    Write-Success "No 'augment' entries found. Database is clean."
                    $connection.Close()
                    return $true
                }

                Write-Info "Found $($entries.Count) entries containing 'augment'"
                for ($i = 0; $i -lt [Math]::Min(5, $entries.Count); $i++) {
                    Write-Info "  - $($entries[$i])"
                }
                if ($entries.Count -gt 5) {
                    Write-Info "  ... and $($entries.Count - 5) more"
                }

                # Delete entries
                $deleteCommand = $connection.CreateCommand()
                $deleteCommand.CommandText = "DELETE FROM ItemTable WHERE key LIKE '%augment%'"
                $deleted = $deleteCommand.ExecuteNonQuery()

                $connection.Close()

                Write-Success "Successfully deleted $deleted entries"
                return $true
            }
            catch {
                Write-Warning "SQLite tools not available. Please install sqlite3.exe or System.Data.SQLite"
                Write-Info "You can download sqlite3.exe from https://www.sqlite.org/download.html"
                Write-Info "Or install System.Data.SQLite NuGet package"
                return $false
            }
        }
    }
    catch {
        Write-Error "Database cleaning failed: $($_.Exception.Message)"
        Restore-Backup $DBPath
        return $false
    }
}

function New-MachineId {
    return ([System.Guid]::NewGuid().ToString("N") + [System.Guid]::NewGuid().ToString("N"))
}

function New-DeviceId {
    return [System.Guid]::NewGuid().ToString()
}

function Update-TelemetryIds {
    param([string]$StoragePath)
    
    if (-not (Test-Path $StoragePath)) {
        Write-Error "Storage file not found: $StoragePath"
        Write-Info "Make sure IDE is installed and has been run at least once"
        return $false
    }
    
    Write-Info "Modifying telemetry IDs: $(Split-Path $StoragePath -Leaf)"
    
    # Create backup
    if (-not (New-Backup $StoragePath)) {
        return $false
    }
    
    try {
        # Generate new IDs
        $newMachineId = New-MachineId
        $newDeviceId = New-DeviceId
        
        Write-Info "New machineId: $newMachineId"
        Write-Info "New devDeviceId: $newDeviceId"
        
        # Read JSON
        $jsonContent = Get-Content $StoragePath -Raw -Encoding UTF8
        $data = $jsonContent | ConvertFrom-Json
        
        $modified = $false
        
        # Update root machineId
        if ($data.PSObject.Properties.Name -contains "machineId") {
            $data.machineId = $newMachineId
            Write-Info "Updated root machineId"
            $modified = $true
        }
        
        # Update telemetry section
        if ($data.PSObject.Properties.Name -contains "telemetry" -and $data.telemetry -is [PSCustomObject]) {
            if ($data.telemetry.PSObject.Properties.Name -contains "machineId") {
                $data.telemetry.machineId = $newMachineId
                Write-Info "Updated telemetry machineId"
                $modified = $true
            }
            
            if ($data.telemetry.PSObject.Properties.Name -contains "devDeviceId") {
                $data.telemetry.devDeviceId = $newDeviceId
                Write-Info "Updated devDeviceId"
                $modified = $true
            }
        }
        
        if (-not $modified) {
            Write-Info "No telemetry IDs found to update"
            return $true
        }
        
        # Write back
        $data | ConvertTo-Json -Depth 10 | Set-Content $StoragePath -Encoding UTF8
        
        Write-Success "Telemetry IDs updated successfully"
        return $true
    }
    catch {
        Write-Error "Telemetry ID modification failed: $($_.Exception.Message)"
        Restore-Backup $StoragePath
        return $false
    }
}

function Confirm-Action {
    param([string]$Question)

    if ($Force) {
        return $true
    }

    do {
        $response = Read-Host "$Question (y/n)"
        $response = $response.Trim().ToLower()

        if ($response -eq "y" -or $response -eq "yes") {
            return $true
        }
        elseif ($response -eq "n" -or $response -eq "no") {
            return $false
        }
        else {
            Write-Error "Please enter 'y' or 'n'"
        }
    } while ($true)
}

function Invoke-AllOperations {
    param([string]$IDEType)

    $config = $IDEConfig[$IDEType]
    $paths = Get-IDEPaths $IDEType
    if (-not $paths) {
        return $false
    }

    Write-Info "Running all operations for $($config.Name)"
    $success = $true

    # Clean database
    Write-Info "Step 1: Cleaning database..."
    if (-not (Clear-Database $paths.StateDB)) {
        Write-Error "Database cleaning failed"
        $success = $false
    }
    Write-Host ""

    # Modify telemetry IDs
    Write-Info "Step 2: Modifying telemetry IDs..."
    if (-not (Update-TelemetryIds $paths.StorageJSON)) {
        Write-Error "Telemetry ID modification failed"
        $success = $false
    }

    if ($success) {
        Write-Success "All operations completed for $($config.Name)!"
        Write-Info "Please restart the IDE to apply changes."
    }
    else {
        Write-Error "Some operations failed for $($config.Name)!"
    }

    return $success
}

function Invoke-SingleOperation {
    param(
        [string]$IDEType,
        [string]$OperationType,
        [hashtable]$Paths
    )

    $config = $IDEConfig[$IDEType]

    switch ($OperationType) {
        "database" {
            Write-ColorMessage "Cleaning $($config.Name) database..." "Yellow"
            Write-Host ("=" * 35)
            Clear-Database $Paths.StateDB
            Write-Host ("=" * 35)
        }
        "telemetry" {
            Write-ColorMessage "Modifying $($config.Name) telemetry IDs..." "Yellow"
            Write-Host ("=" * 40)
            Update-TelemetryIds $Paths.StorageJSON
            Write-Host ("=" * 40)
        }
    }
}

function Find-AvailableIDEs {
    $available = @{}

    foreach ($ideType in $IDEConfig.Keys) {
        $paths = Get-IDEPaths $ideType
        if ($paths) {
            $dbExists = Test-Path $paths.StateDB
            $storageExists = Test-Path $paths.StorageJSON
            $available[$ideType] = $dbExists -or $storageExists
        }
        else {
            $available[$ideType] = $false
        }
    }

    return $available
}

function Show-Menu {
    Write-ColorMessage ("=" * 50) "Cyan"
    Write-ColorMessage "Augment Reset Tool - VS Code & Cursor" "Cyan"
    Write-ColorMessage ("=" * 50) "Cyan"
    Write-Host ""

    # Discover available IDEs
    $availableIDEs = Find-AvailableIDEs

    if (-not ($availableIDEs.Values -contains $true)) {
        Write-Error "No supported IDEs found!"
        Write-Info "Supported IDEs: VS Code, Cursor"
        Read-Host "`nPress Enter to exit..."
        return
    }

    while ($true) {
        Write-ColorMessage "Select IDE:" "Yellow"
        $ideOptions = @()
        $index = 1

        foreach ($ideType in $availableIDEs.Keys) {
            $config = $IDEConfig[$ideType]

            if ($availableIDEs[$ideType]) {
                $status = if (Test-IDERunning $ideType) { "RUNNING" } else { "Ready" }
                $color = if (Test-IDERunning $ideType) { "Yellow" } else { "Green" }
                Write-ColorMessage "$index. $($config.Name) ($status)" $color
                $ideOptions += $ideType
            }
            else {
                Write-ColorMessage "$index. $($config.Name) (Not available)" "Red"
            }
            $index++
        }

        Write-Host "0. Exit"

        try {
            $choice = Read-Host "`nChoice (0-2)"
            if ($choice -eq "0") { break }

            $ideIndex = [int]$choice - 1
            if ($ideIndex -ge 0 -and $ideIndex -lt $ideOptions.Count) {
                $selectedIDE = $ideOptions[$ideIndex]
                $config = $IDEConfig[$selectedIDE]
                $idePaths = Get-IDEPaths $selectedIDE

                if (-not $idePaths) {
                    Write-Error "Cannot get paths for $($config.Name)"
                    continue
                }

                # Feature selection menu
                while ($true) {
                    Write-ColorMessage "`n$($config.Name) - Select Feature:" "Yellow"
                    if (Test-IDERunning $selectedIDE) {
                        Write-Warning "$($config.Name) is currently running!"
                        $closeIDE = Confirm-Action "Close it before proceeding?"
                        if ($closeIDE -and -not (Stop-IDEProcesses $selectedIDE)) {
                            Write-Warning "Failed to close IDE, continuing anyway..."
                        }
                        Write-Host ""
                    }

                    Write-Host "1. Reset Trial (Recommended)"
                    Write-Host "2. Clean Database Only"
                    Write-Host "3. Modify Telemetry IDs Only"
                    Write-Host "0. Back to IDE Selection"

                    $feature = Read-Host "`nChoice (0-3)"

                    if ($feature -eq "0") { break }
                    elseif ($feature -eq "1") {
                        Write-ColorMessage "`nResetting trial for $($config.Name)..." "Yellow"
                        Write-Host ("=" * 50)
                        Invoke-AllOperations $selectedIDE
                        Write-Host ("=" * 50)
                    }
                    elseif ($feature -eq "2") {
                        Invoke-SingleOperation $selectedIDE "database" $idePaths
                    }
                    elseif ($feature -eq "3") {
                        Invoke-SingleOperation $selectedIDE "telemetry" $idePaths
                    }
                    else {
                        Write-Error "Invalid choice!"
                    }
                }
            }
            else {
                Write-Error "Invalid choice!"
            }
        }
        catch {
            Write-Error "Invalid input or interrupted!"
            break
        }
    }

    Write-Info "Thank you for using Augment Reset Tool!"
}

# Main execution
if ($IDE -and $Operation) {
    # Command line mode
    $availableIDEs = Find-AvailableIDEs

    if (-not $availableIDEs[$IDE]) {
        Write-Error "IDE '$IDE' is not available or not installed"
        exit 1
    }

    $paths = Get-IDEPaths $IDE
    if (-not $paths) {
        Write-Error "Cannot get paths for $IDE"
        exit 1
    }

    # Check if IDE is running and close if needed
    if (Test-IDERunning $IDE) {
        Write-Warning "$($IDEConfig[$IDE].Name) is currently running!"
        if ($Force -or (Confirm-Action "Close it before proceeding?")) {
            if (-not (Stop-IDEProcesses $IDE)) {
                Write-Warning "Failed to close IDE, continuing anyway..."
            }
        }
    }

    switch ($Operation) {
        "all" {
            Invoke-AllOperations $IDE
        }
        "database" {
            Invoke-SingleOperation $IDE "database" $paths
        }
        "telemetry" {
            Invoke-SingleOperation $IDE "telemetry" $paths
        }
    }
}
else {
    # Interactive mode
    Show-Menu
}
