"""
IDE Reset Tool
VS Code & Cursor on Windows
Author: <PERSON><PERSON><PERSON><PERSON>
"""

import os, json, sqlite3, uuid, shutil, time, psutil
from pathlib import Path
from enum import Enum
from typing import Dict, List, TypedDict, Any

# Color support
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    has_color = True
except ImportError:
    has_color = False
    class _Fallback:
        def __getattr__(self, _: str) -> str:
            return ''
    Fore = Style = _Fallback()

class IDE(Enum):
    VSCODE = "vscode"
    CURSOR = "cursor"

class IDEConfigItem(TypedDict):
    name: str
    folder: str
    processes: List[str]

# IDE Configuration
IDE_CONFIG: Dict[IDE, IDEConfigItem] = {
    IDE.VSCODE: {
        "name": "VS Code",
        "folder": "Code",
        "processes": ["Code.exe", "Code - Insiders.exe"]
    },
    IDE.CURSOR: {
        "name": "Cursor",
        "folder": "Cursor",
        "processes": ["Cursor.exe"]
    }
}

def print_msg(msg: str, color: str = "") -> None:
    """Print message with optional color."""
    if has_color and color:
        print(f"{color}{msg}{Style.RESET_ALL}")
    else:
        print(msg)

def print_info(msg: str) -> None:
    print_msg(f"[INFO] {msg}", Fore.BLUE)

def print_success(msg: str) -> None:
    print_msg(f"[SUCCESS] {msg}", Fore.GREEN)

def print_error(msg: str) -> None:
    print_msg(f"[ERROR] {msg}", Fore.RED)

def print_warning(msg: str) -> None:
    print_msg(f"[WARNING] {msg}", Fore.YELLOW)

def get_ide_paths(ide: IDE) -> Dict[str, Path]:
    """Get IDE paths for Windows."""
    appdata = os.environ.get("APPDATA")
    if not appdata:
        print_error("APPDATA environment variable not found")
        return {}
    
    config = IDE_CONFIG[ide]
    base_dir = Path(appdata) / config["folder"] / "User"
    
    return {
        "state_db": base_dir / "globalStorage" / "state.vscdb",
        "storage_json": base_dir / "globalStorage" / "storage.json"
    }

def get_running_processes(ide: IDE) -> List[Any]:
    """Get list of running processes for IDE."""
    processes = IDE_CONFIG[ide]["processes"]
    running: List[Any] = []
    try:
        for proc in psutil.process_iter(['pid', 'name']):  # type: ignore
            if proc.info['name'] in processes:
                running.append(proc)
    except:
        pass
    return running

def is_ide_running(ide: IDE) -> bool:
    """Check if IDE is running."""
    return len(get_running_processes(ide)) > 0

def close_ide_processes(ide: IDE) -> bool:
    """Close IDE processes."""
    config = IDE_CONFIG[ide]
    print_info(f"Checking for running {config['name']} processes...")
    
    running_procs = get_running_processes(ide)
    if not running_procs:
        print_info(f"No running {config['name']} processes found")
        return True
    
    try:
        # Graceful termination
        for proc in running_procs:
            try:
                proc.terminate()
                print_info(f"Closed {proc.info['name']} (PID: {proc.info['pid']})")
            except:
                pass
        
        time.sleep(2)  # Wait for graceful shutdown
        
        # Force kill if still running
        still_running = get_running_processes(ide)
        for proc in still_running:
            try:
                proc.kill()
                print_warning(f"Force killed {proc.info['name']}")
            except:
                pass
        
        print_success(f"{config['name']} processes closed")
        return True
    except Exception as e:
        print_error(f"Error closing processes: {e}")
        return False

def create_backup(file_path: Path) -> bool:
    """Create backup of file."""
    if not file_path.exists():
        print_error(f"File not found: {file_path}")
        return False
    
    backup_path = file_path.with_suffix(file_path.suffix + ".backup")
    try:
        shutil.copy2(file_path, backup_path)
        print_success(f"Backup created: {backup_path.name}")
        return True
    except Exception as e:
        print_error(f"Backup failed: {e}")
        return False

def restore_backup(file_path: Path) -> bool:
    """Restore file from backup."""
    backup_path = file_path.with_suffix(file_path.suffix + ".backup")
    if backup_path.exists():
        try:
            shutil.copy2(backup_path, file_path)
            print_success(f"{file_path.name} restored from backup")
            return True
        except Exception as e:
            print_error(f"Restore failed: {e}")
    return False

def clean_database(db_path: Path) -> bool:
    """Clean database entries containing 'augment'."""
    if not db_path.exists():
        print_error(f"Database not found: {db_path}")
        print_info("Make sure IDE is installed and has been run at least once")
        return False
    
    print_info(f"Cleaning database: {db_path.name}")
    
    # Create backup
    if not create_backup(db_path):
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Find entries to delete
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE ?", ("%augment%",))
        entries = cursor.fetchall()
        
        if not entries:
            print_success("No 'augment' entries found. Database is clean.")
            conn.close()
            return True
        
        print_info(f"Found {len(entries)} entries containing 'augment'")
        for entry in entries[:5]:  # Show first 5
            print_info(f"  - {entry[0]}")
        if len(entries) > 5:
            print_info(f"  ... and {len(entries) - 5} more")
        
        # Delete entries
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE ?", ("%augment%",))
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        
        print_success(f"Successfully deleted {deleted} entries")
        return True
        
    except Exception as e:
        print_error(f"Database cleaning failed: {e}")
        restore_backup(db_path)
        return False

def generate_machine_id() -> str:
    """Generate 64-char hex string for machineId."""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_device_id() -> str:
    """Generate UUID for devDeviceId."""
    return str(uuid.uuid4())

def modify_telemetry_ids(storage_path: Path) -> bool:
    """Modify telemetry IDs in storage.json."""
    if not storage_path.exists():
        print_error(f"Storage file not found: {storage_path}")
        print_info("Make sure IDE is installed and has been run at least once")
        return False
    
    print_info(f"Modifying telemetry IDs: {storage_path.name}")
    
    # Create backup
    if not create_backup(storage_path):
        return False
    
    try:
        # Generate new IDs
        new_machine_id = generate_machine_id()
        new_device_id = generate_device_id()
        
        print_info(f"New machineId: {new_machine_id}")
        print_info(f"New devDeviceId: {new_device_id}")
        
        # Read JSON
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        modified = False
        
        # Update root machineId
        if 'machineId' in data:
            data['machineId'] = new_machine_id
            print_info("Updated root machineId")
            modified = True
        
        # Update telemetry section
        if 'telemetry' in data and isinstance(data['telemetry'], dict):
            if 'machineId' in data['telemetry']:
                data['telemetry']['machineId'] = new_machine_id
                print_info("Updated telemetry machineId")
                modified = True
            
            if 'devDeviceId' in data['telemetry']:
                data['telemetry']['devDeviceId'] = new_device_id
                print_info("Updated devDeviceId")
                modified = True
        
        if not modified:
            print_info("No telemetry IDs found to update")
            return True
        
        # Write back
        with open(storage_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4)
        
        print_success("Telemetry IDs updated successfully")
        return True
        
    except Exception as e:
        print_error(f"Telemetry ID modification failed: {e}")
        restore_backup(storage_path)
        return False

def ask_yes_no(question: str) -> bool:
    """Ask user for yes/no confirmation."""
    while True:
        response = input(f"{question} (y/n): ").strip().lower()
        if response in ['y', 'yes']: return True
        elif response in ['n', 'no']: return False
        else: print_error("Please enter 'y' or 'n'")

def run_all(ide: IDE) -> bool:
    """Run all operations with smart IDE handling."""
    config = IDE_CONFIG[ide]
    paths = get_ide_paths(ide)
    if not paths: return False
    
    print_info(f"Running all operations for {config['name']}")
    success = True
    
    # Clean database
    print_info("Step 1: Cleaning database...")
    if not clean_database(paths["state_db"]):
        print_error("Database cleaning failed")
        success = False
    print()
    
    # Modify telemetry IDs
    print_info("Step 2: Modifying telemetry IDs...")
    if not modify_telemetry_ids(paths["storage_json"]):
        print_error("Telemetry ID modification failed")
        success = False
    
    if success:
        print_success(f"All operations completed for {config['name']}!")
        print_info("Please restart the IDE to apply changes.")
    else:
        print_error(f"Some operations failed for {config['name']}!")
    
    return success

def run_single_operation(ide: IDE, operation: str, paths: Dict[str, Path]) -> None:
    """Run a single operation."""
    config = IDE_CONFIG[ide]
    
    if operation == "database":
        print_msg(f"Cleaning {config['name']} database...", Fore.YELLOW)
        print("=" * 35)
        clean_database(paths["state_db"])
        print("=" * 35)
    elif operation == "telemetry":
        print_msg(f"Modifying {config['name']} telemetry IDs...", Fore.YELLOW)
        print("=" * 40)
        modify_telemetry_ids(paths["storage_json"])
        print("=" * 40)

def discover_ides() -> Dict[IDE, bool]:
    """Discover available IDEs."""
    available: Dict[IDE, bool] = {}
    
    for ide in IDE:
        paths = get_ide_paths(ide)
        if paths:
            # Check if at least one key file exists
            db_exists = paths["state_db"].exists()
            storage_exists = paths["storage_json"].exists()
            available[ide] = db_exists or storage_exists
        else:
            available[ide] = False
    
    return available

def main():
    """Main function."""
    print_msg("=" * 50, Fore.CYAN)
    print_msg("IDE Reset Tool - VS Code & Cursor", Fore.CYAN)
    print_msg("=" * 50, Fore.CYAN)
    print()
    
    # Discover available IDEs
    available_ides = discover_ides()
    
    if not any(available_ides.values()):
        print_error("No supported IDEs found!")
        print_info("Supported IDEs: VS Code, Cursor")
        input("\nPress Enter to exit...")
        return
    
    while True:
        print_msg("Select IDE:", Fore.YELLOW)
        ide_options: List[IDE] = []
        for i, (ide, available) in enumerate(available_ides.items(), 1):
            config = IDE_CONFIG[ide]
            
            if available:
                status = "RUNNING" if is_ide_running(ide) else "Ready"
                color = Fore.YELLOW if is_ide_running(ide) else Fore.GREEN
                print_msg(f"{i}. {config['name']} ({status})", color)
                ide_options.append(ide)
            else:
                print_msg(f"{i}. {config['name']} (Not available)", Fore.RED)
        
        print("0. Exit")
        
        try:
            choice = input("\nChoice (0-2): ").strip()
            if choice == "0": break
            
            ide_index = int(choice) - 1
            if 0 <= ide_index < len(ide_options):
                selected_ide = ide_options[ide_index]
                config = IDE_CONFIG[selected_ide]
                ide_paths = get_ide_paths(selected_ide)
                
                if not ide_paths:
                    print_error(f"Cannot get paths for {config['name']}")
                    continue
                
                # Feature selection menu
                while True:
                    print_msg(f"\n{config['name']} - Select Feature:", Fore.YELLOW)
                    if is_ide_running(selected_ide):
                        print_warning(f"{config['name']} is currently running!")
                        close_ide = ask_yes_no("Close it before proceeding?")
                        if close_ide and not close_ide_processes(selected_ide):
                            print_warning("Failed to close IDE, continuing anyway...")
                        print()
                    
                    print("1. Reset Trial (Recommended)")
                    print("2. Clean Database Only")
                    print("3. Modify Telemetry IDs Only")
                    print("0. Back to IDE Selection")
                    
                    feature = input("\nChoice (0-3): ").strip()
                    
                    if feature == "0": break
                    elif feature == "1":
                        print_msg(f"\nResetting trial for {config['name']}...", Fore.YELLOW)
                        print("=" * 50)
                        run_all(selected_ide)
                        print("=" * 50)
                    elif feature == "2":
                        run_single_operation(selected_ide, "database", ide_paths)
                    elif feature == "3":
                        run_single_operation(selected_ide, "telemetry", ide_paths)
                    else:
                        print_error("Invalid choice!")
            else:
                print_error("Invalid choice!")
        except (ValueError, KeyboardInterrupt):
            print_error("Invalid input or interrupted!")
            break
    
    print_info("Thank you for using IDE Reset Tool!")

if __name__ == "__main__":
    main()